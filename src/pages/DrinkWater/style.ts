import { StyleSheet } from 'react-native';
import { px } from 'utils/px';
import { Dimensions } from 'react-native';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export const getStyles = () => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: '#E5F2FF',
    },
    scrollView: {
      flex: 1,
    },
    scrollContent: {
      paddingBottom: px(67),
      position: 'relative',
    },
    contentWrapper: {
      position: 'relative',
      zIndex: 2,
    },
    backgroundImage: {
      position: 'absolute',
      top: 0,
      left: 0,
      width: SCREEN_WIDTH,
      height: undefined,
      resizeMode: 'cover',
      aspectRatio: 375 / 321,
      zIndex: 1,
    },
    ruleButton: {
      position: 'absolute',
      top: px(11),
      right: px(10),
      zIndex: 3,
      borderRadius: px(10),
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: px(1),
      borderColor: '#FFFFFF',
      paddingHorizontal: px(10),
      paddingVertical: px(2),
    },
    ruleText: {
      fontSize: px(12),
      fontWeight: '600',
      color: '#366EA4',
    },
  });
};
