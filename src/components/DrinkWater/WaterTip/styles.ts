import { StyleSheet } from 'react-native';

export const getStyles = () => {
  return StyleSheet.create({
    container: {
      backgroundColor: '#FFFFFF',
      marginHorizontal: 16,
      marginTop: 20,
      borderRadius: 5,
      overflow: 'hidden',
    },
    backgroundIcon: {
      position: 'absolute',
      right: 0,
      width: 84,
      height: 88,
      zIndex: 1,
    },
    textContainer: {
      width: '100%',
      paddingHorizontal: 16,
      paddingVertical: 20,
      zIndex: 2,
      position: 'relative',
    },
    title: {
      fontSize: 16,
      fontWeight: '600',
      color: '#131415',
      marginBottom: 8,
      width: '100%',
    },
    subTitle: {
      fontSize: 13,
      color: '#999999',
      width: '100%',
    },
  });
};
