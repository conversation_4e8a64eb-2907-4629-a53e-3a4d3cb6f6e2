import React from 'react';
import { View, Text, Image } from 'react-native';
import { useAtomValue } from 'jotai';
import { waterInfoAtom } from 'pages/DrinkWater/store';
import { getStyles } from './styles';

export default function WaterTip() {
  const styles = getStyles();
  const waterInfo = useAtomValue(waterInfoAtom);

  if (!waterInfo?.waterTip) {
    return null;
  }

  const { waterTip } = waterInfo;

  return (
    <View style={styles.container}>
      {waterTip.icon ? (
        <Image
          source={{ uri: waterTip.icon }}
          style={styles.backgroundIcon}
          resizeMode="contain"
        />
      ) : null}
      <View style={styles.textContainer}>
        <Text style={styles.title} numberOfLines={1} ellipsizeMode="tail">
          {waterTip.title}
        </Text>
        <Text style={styles.subTitle} numberOfLines={1} ellipsizeMode="tail">
          {waterTip.subTitle}
        </Text>
      </View>
    </View>
  );
}
