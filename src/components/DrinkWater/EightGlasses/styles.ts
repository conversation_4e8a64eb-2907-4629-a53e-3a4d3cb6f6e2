import { StyleSheet } from 'react-native';

export const getStyles = () => {
  return StyleSheet.create({
    container: {
      marginHorizontal:16,
      marginTop: 70,
      position: 'relative',
    },
    catImage: {
      width: 79,
      height: 55,
      resizeMode: "cover",
      zIndex: 2,
      position: 'absolute',
      right: 0,
      top: -31
    },
    glassesContainer: {
      backgroundColor: '#FFFFFF',
      borderRadius: 5,
      paddingHorizontal: 12,
      paddingVertical: 24,
      zIndex: 1
    },
    row: {
      flexDirection: 'row',
      marginBottom: 16,
      justifyContent: 'flex-start',
    },
    lastRow: {
      marginBottom: 0,
    },
    glassWrapper: {
      // 固定宽度，不使用 flex
    },
    glassMarginRight: {
      marginRight: 12,
    },
    loadingContainer: {
      height: 200,
      alignItems: 'center',
      justifyContent: 'center',
    },
    loadingText: {
      fontSize: 14,
      color: '#999999',
    },
    errorContainer: {
      height: 200,
      alignItems: 'center',
      justifyContent: 'center',
    },
    errorText: {
      fontSize: 14,
      color: '#999999',
    },
  });
};
