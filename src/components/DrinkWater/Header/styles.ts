import { StyleSheet } from 'react-native';
import { px } from 'utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    paddingTop: px(20),
    paddingBottom: px(10),
    zIndex: 100,
    flexDirection: 'row',
    justifyContent: 'center',
    backgroundColor: theme.bgColor,
  },
  title: {
    fontSize: px(17),
    lineHeight: px(24),
    color: theme.titleColor,
  },
  backBtn: {
    position: 'absolute',
    left: 16,
  },
});
