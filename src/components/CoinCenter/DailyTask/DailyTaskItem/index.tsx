import React, { useEffect, useState, useContext } from "react";
import { View, Text, Image } from "react-native";
import { getStyles } from "./styles";
import { useAtomValue, useSetAtom } from "jotai";
import dailyTaskItemThemeAtom from "./theme";
import { DailyTaskItem as DailyTaskItemType, DailyTaskType } from "services/welfare";
import { formatSeconds } from "utils/time";
import { useCountdown } from "../hooks/useCountdown";
import watchAd from "utils/watchAd";
import { FallbackReqType, DAILY_TASK_TYPE_TO_AD_SOURCE } from "constants/ad";
import { updateDailyTaskAtom } from "../atom";
import { AD_SOURCE, RewardType } from "constants/ad";
import useRewardGoldCoin from "hooks/useRewardGoldCoin";
import { ScrollAnalyticComp } from "@xmly/react-native-page-analytics";
import xmlog from "utilsV2/xmlog";
import TaskButton from "components/CoinCenter/common/TaskButton";
import { useGameLaunch } from "components/CoinCenter/RedPacketRain";
import { DarkModeContext } from '../../../../contextV2/darkModeContext';
import { useNavigation } from "@react-navigation/native";
import { RootNavigationProps } from "router/type";
import { Page } from '@xmly/rn-sdk';

interface DailyTaskItemProps {
  item: DailyTaskItemType;
  countdown: number;
  isFirst?: boolean;
  loading?: boolean;
  updatingPositions: string[];
  index: number;
  onRewardCoinFinish: (coin: number) => void;
}

export default function DailyTaskItem({
  item,
  countdown,
  isFirst,
  loading,
  index,
  updatingPositions,
  onRewardCoinFinish,
}: DailyTaskItemProps) {
  const { isDarkMode } = useContext(DarkModeContext)
  const theme = useAtomValue(dailyTaskItemThemeAtom);
  const styles = getStyles(theme);
  const key = `${item.positionId}${item.title}`;
  const isUpdating = updatingPositions.includes(key);
  const updateDailyTask = useSetAtom(updateDailyTaskAtom);
  const rewardGoldCoin = useRewardGoldCoin();
  const [isProcessing, setIsProcessing] = useState(false);
  const [btnText, setBtnText] = useState('');
  const navigation = useNavigation<RootNavigationProps>();

  // 使用统一的红包雨钩子
  const { preloadResources, launchGame } = useGameLaunch();

  useCountdown({
    key,
    positionId: item.positionId,
    initialSeconds: countdown,
  });

  async function handlePress() {
    clickReport();
    if (isProcessing) return;

    try {
      setIsProcessing(true);

      // 处理喝水任务类型 - 跳转到喝水任务页面
      if (item.type === DailyTaskType.DRINK_WATER) {
        // 跳转到喝水任务页面
        Page.start('iting://open?msg_type=94&__debug=1&bundle=rn_credit_center&ip=************:8081&__ip=************:8081&drinkWater=1&embed=1')
        // navigation.navigate('DrinkWater');
        setIsProcessing(false);
        return;
      }

      // 处理红包雨任务类型
      if (item.type === DailyTaskType.RED_PACKET_RAIN) {
        // 启动红包雨游戏，并传入回调函数更新任务
        await launchGame(item, AD_SOURCE.RED_PACKET_RAIN, () => {
          updateDailyTask(key);
        });
        setIsProcessing(false);
        return;
      }

      // 处理普通广告任务
      const res = await watchAd({
        positionName: item.positionName,
        slotId: item.positionId,
        extInfo: item.extMap,
        sourceName: DAILY_TASK_TYPE_TO_AD_SOURCE[item.type ?? ''] ?? AD_SOURCE.DAILY,
      });

      if (res.success) {
        const result = await rewardGoldCoin({
          extMap: item.extMap,
          rewardType: item?.type === DailyTaskType.TREASURE_BOX ? RewardType.TREASURE_BOX : RewardType.DAILY,
          coins: item.coins,
          sourceName: DAILY_TASK_TYPE_TO_AD_SOURCE[item.type ?? ''] ?? AD_SOURCE.DAILY,
          adId: res.adId,
          adResponseId: res.adResponseId,
          ecpm: res.ecpm,
          encryptType: res.encryptType,
          fallbackReq: res?.fallbackReq ?? FallbackReqType.NORMAL,
        }, true);

        if (result?.success) {
          updateDailyTask(key);
          onRewardCoinFinish(result.coins);
        }
      }
    } catch (error) {
      console.error('Failed to handle daily task:', error);
    } finally {
      setIsProcessing(false);
    }
  }

  function onShow() {
    // 如果是红包雨任务类型，提前预加载资源
    if (item.type === DailyTaskType.RED_PACKET_RAIN) {
      preloadResources();
    }

    // 福利中心-每日任务  控件曝光
    xmlog.event(67696, 'slipPage', {
      currPage: 'welfareCenter',
      moduleTitle: '每日任务',
      taskTitle: item.title,
      taskId: `${item.positionId}`,
      positionNew: `${index + 1}`,
      Item: btnText
    });
  }

  function clickReport() {
    // 福利中心-任务-任务条  点击事件
    xmlog.click(67695, 'DailyTaskItem', {
      currPage: 'welfareCenter',
      moduleTitle: '每日任务',
      taskTitle: item.title,
      taskId: `${item.positionId}`,
      positionNew: `${index + 1}`,
      Item: btnText
    });
  }

  function renderButton() {
    if (countdown > 0) {
      return <Text style={styles.countdown}>{formatSeconds(countdown)}后领取</Text>;
    }

    const buttonDisabled = isUpdating || isProcessing;

    return (
      <TaskButton
        text={item?.btnText ?? '去领取'}
        style={[
          styles.taskButton,
          buttonDisabled && styles.loadingButton
        ]}
        disabled={buttonDisabled}
        onPress={handlePress}
      />
    );
  }

  useEffect(() => {
    if (countdown > 0) {
      setBtnText(`${formatSeconds(countdown)}后领取`);
    } else {
      setBtnText(item.btnText ?? '去领取');
    }
  }, [countdown]);

  return (
    <ScrollAnalyticComp
      itemKey={`DailyTaskItem_${item.positionId}_${item.title}`}
      onShow={onShow}
    >
      <View style={[styles.taskItem, isFirst && styles.firstTaskItem]}>
        {
          (item.darkIcon || item.icon) ? <Image style={styles.coinIcon} source={{ uri: isDarkMode ? item.darkIcon : item.icon }} />
            : null
        }
        <View style={styles.taskInfo}>
          <View style={styles.taskNameView}>
            <Text numberOfLines={1} style={styles.taskName}>{item.title}</Text>
            {
              item.maxAmount ? <View style={styles.taskSubTitle}>
                <Image style={styles.taskSubTitleCoin} source={{ uri: 'https://imagev2.xmcdn.com/storages/c00d-audiofreehighqps/3E/2E/GAqh1QQMFFVRAAAEvAO-jCTD.png' }} />
                <Text style={styles.taskSubTitleText}>最高+{item.maxAmount}</Text>
              </View> : null
            }

          </View>
          <Text style={styles.taskReward}>{item.subTitle}</Text>
        </View>
        {renderButton()}
      </View>
    </ScrollAnalyticComp>
  );
}

